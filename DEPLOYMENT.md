# 🚀 Mai Voice Agent - Deployment Guide

## 📋 Pre-Deployment Checklist

### ✅ Features Implemented
- **🎤 Real-time WebRTC Voice Chat** with FastRTC integration
- **📹 Video Chat** with <PERSON>'s animated GIF avatar
- **🌊 Wave Animations** during Mai's speech
- **💬 Text Chat** with WebSocket real-time communication
- **📝 Contact Form** with email integration
- **🎨 Modern Dark UI** with responsive design

### ✅ Technical Stack
- **Backend**: FastAPI + Python 3.13
- **Frontend**: Vanilla JavaScript + CSS3
- **AI**: Google Gemini 2.0 Flash Experimental
- **Voice**: Aoede voice synthesis
- **Real-time**: WebRTC + FastRTC + WebSockets
- **Deployment**: Railway + Docker

## 🔧 Environment Variables Required

Create a `.env` file with the following variables:

```env
# Google Gemini AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Email Configuration (for contact form)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password_here
FROM_EMAIL=<EMAIL>
TO_EMAIL=<EMAIL>

# Application Configuration
ENVIRONMENT=production
DEBUG=false
PORT=8000
HOST=0.0.0.0

# CORS Configuration (optional)
ALLOWED_ORIGINS=["https://your-domain.com"]
```

## 🚂 Railway Deployment

### 1. Prepare Repository
```bash
# Remove unnecessary files (already done)
# Ensure .gitignore is properly configured (already done)
# Verify requirements.txt includes all dependencies (already done)
```

### 2. Deploy to Railway
1. **Connect Repository**: Link your GitHub repository to Railway
2. **Set Environment Variables**: Add all variables from `.env` file
3. **Configure Build**: Railway will auto-detect Python and use `requirements.txt`
4. **Deploy**: Railway will automatically build and deploy

### 3. Railway Configuration Files
- `railway.json` ✅ (already configured)
- `Procfile` ✅ (already configured)
- `nixpacks.toml` ✅ (already configured)

## 🐳 Docker Deployment (Alternative)

### Build and Run
```bash
# Build Docker image
docker build -t mai-voice-agent .

# Run container
docker run -p 8000:8000 --env-file .env mai-voice-agent
```

## 🔍 Testing Deployment

### 1. Health Check
```bash
curl https://your-domain.railway.app/api/health
```

### 2. Test Features
- **Text Chat**: Open the app and test text messaging
- **Voice Chat**: Click voice chat and test microphone/audio
- **Video Chat**: Test video chat with Mai's avatar and wave animations
- **Contact Form**: Submit a test contact form

### 3. Monitor Logs
- Check Railway logs for any errors
- Monitor WebSocket connections
- Verify WebRTC fallback behavior

## 📊 Performance Optimization

### 1. FastRTC Configuration
- Real-time voice processing with ultra-low latency
- Automatic fallback to basic voice if FastRTC unavailable
- WebRTC peer-to-peer connections for optimal performance

### 2. Caching Strategy
- Static assets served efficiently
- Session management with memory optimization
- Conversation history managed per session

### 3. Error Handling
- Graceful degradation for voice features
- Comprehensive error logging
- User-friendly error messages

## 🔒 Security Considerations

### 1. API Keys
- Store all sensitive data in environment variables
- Never commit API keys to repository
- Use Railway's secure environment variable storage

### 2. CORS Configuration
- Configure allowed origins for production
- Restrict WebSocket connections to trusted domains
- Implement rate limiting for API endpoints

### 3. Input Validation
- All user inputs are validated and sanitized
- File upload restrictions in place
- SQL injection protection (not applicable - no database)

## 📈 Monitoring & Maintenance

### 1. Health Monitoring
- `/api/health` endpoint for uptime monitoring
- WebSocket connection status tracking
- Voice session management

### 2. Logging
- Comprehensive logging for all operations
- Error tracking and debugging information
- Performance metrics collection

### 3. Updates
- Regular dependency updates
- Gemini API version monitoring
- Feature enhancement tracking

## 🎯 Production Checklist

- [ ] Environment variables configured
- [ ] GEMINI_API_KEY is valid and has sufficient quota
- [ ] Email credentials are working
- [ ] Domain is configured (if using custom domain)
- [ ] SSL certificate is active
- [ ] Health check endpoint responds
- [ ] All chat modes are functional
- [ ] Wave animations are working
- [ ] Mai avatar is displaying correctly
- [ ] Contact form sends emails
- [ ] WebRTC voice chat is operational
- [ ] Error handling is graceful
- [ ] Logs are being generated properly

## 🆘 Troubleshooting

### Common Issues
1. **Voice Chat Not Working**: Check microphone permissions and WebRTC support
2. **API Quota Exceeded**: Monitor Gemini API usage and upgrade plan if needed
3. **Email Not Sending**: Verify SMTP credentials and app password
4. **WebSocket Errors**: Check CORS configuration and network connectivity
5. **FastRTC Fallback**: Normal behavior when FastRTC is unavailable

### Support
- Check Railway logs for detailed error information
- Monitor browser console for frontend issues
- Verify all environment variables are set correctly
- Test individual components (text, voice, video, contact)

## 🎉 Success!

Your Mai Voice Agent is now deployed and ready to provide intelligent, real-time voice assistance with beautiful wave animations and Mai's charming personality!

**Live Features:**
- 🎤 Real-time voice chat with Aoede voice
- 📹 Video chat with animated Mai avatar
- 🌊 Wave animations during speech
- 💬 Instant text messaging
- 📝 Professional contact form
- 🎨 Beautiful dark theme UI

**Access your deployment at**: `https://your-app-name.railway.app`

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Therapist - Voice, Video & Text</title>
    <style>
        :root {
            --color-accent: #6366f1;
            --color-background: #0f172a;
            --color-surface: #1e293b;
            --color-text: #e2e8f0;
            --color-success: #10b981;
            --color-warning: #f59e0b;
            --color-error: #ef4444;
            --boxSize: 8px;
            --gutter: 4px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, var(--color-background) 0%, #1e1b4b 100%);
            color: var(--color-text);
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 1rem;
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #6366f1, #8b5cf6, #06b6d4);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 0.5rem;
        }

        .header .subtitle {
            color: #94a3b8;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .disclaimer {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.3);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            background: rgba(30, 41, 59, 0.95);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .mode-selector {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
            background: rgba(15, 23, 42, 0.5);
            border-radius: 0.75rem;
            padding: 0.5rem;
        }

        .mode-btn {
            flex: 1;
            padding: 0.75rem 1.5rem;
            border: none;
            background: transparent;
            color: var(--color-text);
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .mode-btn.active {
            background: var(--color-accent);
            color: white;
        }

        .mode-btn:hover:not(.active) {
            background: rgba(99, 102, 241, 0.2);
        }

        /* Voice Mode Styles */
        .voice-section {
            display: none;
        }

        .voice-section.active {
            display: block;
        }

        .controls {
            display: grid;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .input-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #cbd5e1;
        }

        input, select {
            padding: 0.75rem;
            border-radius: 0.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background-color: rgba(15, 23, 42, 0.8);
            color: var(--color-text);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        input:focus, select:focus {
            outline: none;
            border-color: var(--color-accent);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .wave-container {
            position: relative;
            display: flex;
            min-height: 120px;
            max-height: 140px;
            justify-content: center;
            align-items: center;
            margin: 2rem 0;
            background: rgba(15, 23, 42, 0.3);
            border-radius: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .box-container {
            display: flex;
            justify-content: space-between;
            height: 64px;
            width: 100%;
            max-width: 400px;
            padding: 0 1rem;
        }

        .box {
            height: 100%;
            width: var(--boxSize);
            background: linear-gradient(135deg, var(--color-accent), #8b5cf6);
            border-radius: 8px;
            transition: transform 0.05s ease;
            margin: 0 1px;
        }

        /* Video Mode Styles */
        .video-section {
            display: none;
        }

        .video-section.active {
            display: block;
        }

        .video-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 2rem 0;
            height: 400px;
        }

        .video-panel {
            background: rgba(15, 23, 42, 0.8);
            border-radius: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .video-element {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 1rem;
        }

        .video-label {
            position: absolute;
            top: 10px;
            left: 15px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            z-index: 10;
        }

        .video-status {
            position: absolute;
            bottom: 10px;
            right: 15px;
            background: rgba(16, 185, 129, 0.9);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            z-index: 10;
        }

        .file-upload-panel {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 2rem;
        }

        .upload-zone {
            border: 2px dashed rgba(99, 102, 241, 0.3);
            border-radius: 1rem;
            padding: 2rem;
            background: rgba(99, 102, 241, 0.05);
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .upload-zone:hover {
            border-color: var(--color-accent);
            background: rgba(99, 102, 241, 0.1);
        }

        .upload-zone.dragover {
            border-color: var(--color-accent);
            background: rgba(99, 102, 241, 0.15);
        }

        .upload-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.6;
        }

        .upload-text {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .upload-subtext {
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .file-input {
            display: none;
        }

        .uploaded-files {
            margin-top: 1rem;
            max-height: 150px;
            overflow-y: auto;
        }

        .uploaded-file {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            margin: 0.5rem 0;
            display: flex;
            justify-content: between;
            align-items: center;
            font-size: 0.9rem;
        }

        .file-remove {
            background: none;
            border: none;
            color: var(--color-error);
            cursor: pointer;
            margin-left: auto;
            padding: 0.25rem;
        }

        .video-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .video-btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            border: none;
            background: linear-gradient(135deg, var(--color-accent), #8b5cf6);
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-width: 150px;
        }

        .video-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
        }

        .video-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .video-btn.danger {
            background: linear-gradient(135deg, var(--color-error), #dc2626);
        }

        .voice-button {
            padding: 1rem 2rem;
            border-radius: 0.75rem;
            border: none;
            background: linear-gradient(135deg, var(--color-accent), #8b5cf6);
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            min-width: 200px;
            margin: 0 auto;
            font-size: 1.1rem;
        }

        .voice-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
        }

        .voice-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .voice-button.danger {
            background: linear-gradient(135deg, var(--color-error), #dc2626);
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid white;
            border-top-color: transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            flex-shrink: 0;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .pulse-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .pulse-circle {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: white;
            opacity: 0.7;
            flex-shrink: 0;
            transform: scale(var(--audio-level, 1));
            transition: transform 0.1s ease;
        }

        .mute-toggle {
            width: 28px;
            height: 28px;
            cursor: pointer;
            flex-shrink: 0;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .mute-toggle:hover {
            opacity: 1;
        }

        .mute-toggle svg {
            display: block;
            width: 100%;
            height: 100%;
        }

        /* Text Mode Styles */
        .text-section {
            display: none;
        }

        .text-section.active {
            display: block;
        }

        .chat-container {
            height: 50vh;
            border-radius: 0.75rem;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(15, 23, 42, 0.5);
        }

        .messages {
            height: calc(100% - 80px);
            overflow-y: auto;
            padding: 1rem;
        }

        .message {
            display: flex;
            margin-bottom: 1rem;
            animation: fadeIn 0.3s ease-in-out;
        }

        .user-message {
            flex-direction: row-reverse;
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            background: linear-gradient(135deg, var(--color-accent), #8b5cf6);
            color: white;
            font-size: 1.2rem;
        }

        .user-message .avatar {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
        }

        .bubble {
            max-width: 70%;
            padding: 12px 15px;
            border-radius: 18px;
            position: relative;
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .ai-message .bubble {
            border-top-left-radius: 5px;
        }

        .user-message .bubble {
            border-top-right-radius: 5px;
            background: rgba(6, 182, 212, 0.2);
            border-color: rgba(6, 182, 212, 0.3);
        }

        .bubble p {
            margin: 0;
            line-height: 1.5;
        }

        .input-area {
            display: flex;
            padding: 1rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(15, 23, 42, 0.8);
        }

        .text-input {
            flex: 1;
            border: none;
            padding: 10px 15px;
            border-radius: 20px;
            resize: none;
            background: rgba(30, 41, 59, 0.8);
            color: var(--color-text);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            font-family: inherit;
            font-size: 1rem;
        }

        .text-input:focus {
            outline: none;
            border-color: var(--color-accent);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .input-buttons {
            display: flex;
            gap: 0.5rem;
            margin-left: 0.75rem;
        }

        .input-btn {
            background: none;
            border: none;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            color: var(--color-accent);
        }

        .input-btn:hover {
            background-color: rgba(99, 102, 241, 0.1);
        }

        .input-btn:active {
            background: var(--color-accent);
            color: white;
            animation: pulse 1.5s infinite;
        }

        /* Footer Styles */
        .footer {
            width: 100%;
            background: rgba(15, 23, 42, 0.95);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            text-align: center;
            margin-top: 3rem;
        }

        .footer-content {
            font-size: 0.9rem;
            color: #94a3b8;
        }

        .footer-link {
            color: var(--color-accent);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .footer-link:hover {
            text-decoration: underline;
            transform: translateY(-1px);
        }

        /* Toast Notifications */
        .toast {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 16px 24px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
            display: none;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
        }

        .toast.error {
            background: rgba(239, 68, 68, 0.9);
            color: white;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .toast.warning {
            background: rgba(245, 158, 11, 0.9);
            color: white;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .toast.success {
            background: rgba(16, 185, 129, 0.9);
            color: white;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        /* Status indicators */
        .status-indicator {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(30, 41, 59, 0.9);
            color: var(--color-text);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            opacity: 0;
            transition: opacity 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
        }

        .status-indicator.visible {
            opacity: 1;
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                width: 95%;
                padding: 1.5rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .bubble {
                max-width: 85%;
            }

            .mode-selector {
                flex-direction: column;
                gap: 0.5rem;
            }

            .chat-container {
                height: 60vh;
            }

            .voice-button, .video-btn {
                min-width: 140px;
                font-size: 0.9rem;
            }

            .video-container {
                grid-template-columns: 1fr;
                height: 300px;
            }

            .video-controls {
                flex-direction: column;
                align-items: center;
            }

            .footer {
                padding: 1.25rem;
            }

            .footer-content {
                font-size: 0.8rem;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.8rem;
            }
            
            .bubble {
                max-width: 90%;
            }

            .input-area {
                flex-direction: column;
                gap: 0.75rem;
            }

            .input-buttons {
                justify-content: center;
                margin-left: 0;
            }

            .footer-content {
                font-size: 0.75rem;
                line-height: 1.4;
            }
        }
    </style>
</head>

<body>
    <!-- Toast Notifications -->
    <div id="error-toast" class="toast"></div>

    <!-- Header -->
    <div class="header">
        <h1>🧠 AI Therapist</h1>
        <p class="subtitle">A safe space to talk about your feelings and emotions in Voice, Video & Text Therapeutic Support</p>
        <div class="disclaimer">
            <strong>⚠️ Important:</strong> This AI is not a substitute for professional mental health services. 
            If you're experiencing a crisis, please contact emergency services or a mental health helpline immediately.
        </div>
    </div>

    <!-- Main Container -->
    <div class="container">
        <!-- Mode Selector -->
        <div class="mode-selector">
            <button class="mode-btn" data-mode="voice">🎤 Voice Chat</button>
            <button class="mode-btn" data-mode="video">📹 Video Chat</button>
            <button class="mode-btn active" data-mode="text">💬 Text Chat</button>
        </div>

        <!-- Voice Mode -->
        <div class="voice-section">
            <div class="controls">
                <div class="input-group">
                    <label for="voice">Therapeutic Voice</label>
                    <select id="voice">
                        <option value="Aoede">Aoede (Recommended - Melodic & Soothing)</option>
                        <option value="Kore">Kore (Gentle & Empathetic)</option>
                        <option value="Charon">Charon (Calm & Reassuring)</option>
                        <option value="Puck">Puck (Warm & Conversational)</option>
                        <option value="Fenrir">Fenrir (Strong & Supportive)</option>
                    </select>
                </div>
            </div>

            <div class="wave-container">
                <div class="box-container">
                    <!-- Audio visualization bars will be added here -->
                </div>
            </div>

            <button id="start-button" class="voice-button">
                🎤 Start Voice Session
            </button>
        </div>

        <!-- Video Mode -->
        <div class="video-section">
            <div class="controls">
                <div class="input-group">
                    <label for="video-voice">Therapeutic Voice for Video</label>
                    <select id="video-voice">
                        <option value="Aoede">Aoede (Recommended - Melodic & Soothing)</option>
                        <option value="Kore">Kore (Gentle & Empathetic)</option>
                        <option value="Charon">Charon (Calm & Reassuring)</option>
                        <option value="Puck">Puck (Warm & Conversational)</option>
                        <option value="Fenrir">Fenrir (Strong & Supportive)</option>
                    </select>
                </div>
            </div>

            <div class="video-container">
                <div class="video-panel">
                    <div class="video-label">Your Video</div>
                    <video id="local-video" class="video-element" autoplay muted playsinline></video>
                    <div class="video-status" id="local-status">Camera Ready</div>
                </div>
                <div class="video-panel">
                    <div class="video-label">📁 Share Files with AI</div>
                    <div class="file-upload-panel">
                        <div class="upload-zone" id="upload-zone">
                            <div class="upload-icon">📄</div>
                            <div class="upload-text">Click or drag files here</div>
                            <div class="upload-subtext">Share documents, images, or any files with your AI Therapist</div>
                        </div>
                        <input type="file" id="file-input" class="file-input" multiple accept="*/*">
                        <div class="uploaded-files" id="uploaded-files"></div>
                    </div>
                </div>
            </div>

            <div class="video-controls">
                <button id="start-video-button" class="video-btn">
                    📹 Start Video Session
                </button>
                <button id="toggle-camera" class="video-btn" style="display: none;">
                    📷 Toggle Camera
                </button>
                <button id="toggle-mic" class="video-btn" style="display: none;">
                    🎤 Toggle Mic
                </button>
            </div>
        </div>

        <!-- Text Mode -->
        <div class="text-section active">
            <div class="chat-container">
                <div class="messages" id="messages">
                    <div class="message ai-message">
                        <div class="avatar">🤖</div>
                        <div class="bubble">
                            <p>Hello! I'm your AI Therapist created by Critical Future. I'm here to listen and support you. How are you feeling today? You can type your thoughts, use voice, or even video chat with me!</p>
                        </div>
                    </div>
                </div>

                <div class="input-area">
                    <textarea class="text-input" id="user-input" placeholder="Share your thoughts and feelings..." rows="1"></textarea>
                    <div class="input-buttons">
                        <button class="input-btn" id="mic-btn" title="Speak">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                                <line x1="12" y1="19" x2="12" y2="23"></line>
                                <line x1="8" y1="23" x2="16" y2="23"></line>
                            </svg>
                        </button>
                        <button class="input-btn" id="send-btn" title="Send">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M2 21l21-9L2 3v7l15 2-15 2v7z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <div class="footer-content">
            AI Therapist Powered by <a href="https://criticalfutureglobal.com/" target="_blank" rel="noopener noreferrer" class="footer-link">Critical Future</a>
        </div>
    </div>

    <!-- Status Indicator -->
    <div class="status-indicator" id="status">
        <span id="status-text">Ready</span>
    </div>

    <!-- Audio Element for Playback -->
    <audio id="audio-output" autoplay></audio>

    <script>
        // Global variables
        let peerConnection;
        let videoPeerConnection;
        let audioContext;
        let dataChannel;
        let videoDataChannel;
        let isRecording = false;
        let isVideoRecording = false;
        let webrtc_id;
        let video_webrtc_id;
        let isMuted = false;
        let isCameraOn = true;
        let analyser_input, dataArray_input;
        let analyser, dataArray;
        let source_input = null;
        let source_output = null;
        let currentMode = 'text';
        let localStream = null;
        let uploadedFiles = [];

        // DOM Elements
        const startButton = document.getElementById('start-button');
        const startVideoButton = document.getElementById('start-video-button');
        const toggleCameraBtn = document.getElementById('toggle-camera');
        const toggleMicBtn = document.getElementById('toggle-mic');
        const voiceSelect = document.getElementById('voice');
        const videoVoiceSelect = document.getElementById('video-voice');
        const audioOutput = document.getElementById('audio-output');
        const localVideo = document.getElementById('local-video');
        const localStatus = document.getElementById('local-status');
        const boxContainer = document.querySelector('.box-container');
        const messagesContainer = document.getElementById('messages');
        const userInput = document.getElementById('user-input');
        const sendButton = document.getElementById('send-btn');
        const micButton = document.getElementById('mic-btn');
        const statusIndicator = document.getElementById('status');
        const statusText = document.getElementById('status-text');
        const uploadZone = document.getElementById('upload-zone');
        const fileInput = document.getElementById('file-input');
        const uploadedFilesContainer = document.getElementById('uploaded-files');

        // Mode switching
        const modeButtons = document.querySelectorAll('.mode-btn');
        const voiceSection = document.querySelector('.voice-section');
        const videoSection = document.querySelector('.video-section');
        const textSection = document.querySelector('.text-section');
        const RTC_CONFIGURATION = {
        iceServers: [
         { urls: ["stun:fr-turn7.xirsys.com"] },
         { urls: ["stun:stun.l.google.com:19302"] },
         { urls: ["stun:stun1.l.google.com:19302"] },
         {
          username: "UIuBt8vNm5tNifOx-1ZY-nlw-mNMjhzc_2LsV1Wjpu2ccJ8-u_6wlgw0j7TxEvi6AAAAAGhSBQhNb29tYXJh",
          credential: "48a4de2c-4bd9-11f0-a9be-6aee953622e2",
          urls: [
          "turn:fr-turn7.xirsys.com:80?transport=udp",
          "turn:fr-turn7.xirsys.com:3478?transport=udp",
          "turn:fr-turn7.xirsys.com:80?transport=tcp",
          "turn:fr-turn7.xirsys.com:3478?transport=tcp",
          "turns:fr-turn7.xirsys.com:443?transport=tcp",
          "turns:fr-turn7.xirsys.com:5349?transport=tcp"
      ]
    }
  ]
};

        modeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const mode = btn.dataset.mode;
                switchMode(mode);
            });
        });

        function switchMode(mode) {
            currentMode = mode;
            
            modeButtons.forEach(btn => {
                btn.classList.toggle('active', btn.dataset.mode === mode);
            });

            // Hide all sections
            voiceSection.classList.remove('active');
            videoSection.classList.remove('active');
            textSection.classList.remove('active');

            // Show selected section
            if (mode === 'voice') {
                voiceSection.classList.add('active');
            } else if (mode === 'video') {
                videoSection.classList.add('active');
                initializeVideoMode();
            } else {
                textSection.classList.add('active');
                initializeTextMode();
            }
        }

        // File upload functionality
        uploadZone.addEventListener('click', () => {
            fileInput.click();
        });

        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', () => {
            uploadZone.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            handleFiles(files);
        });

        fileInput.addEventListener('change', (e) => {
            const files = e.target.files;
            handleFiles(files);
        });

        async function handleFiles(files) {
            for (let file of files) {
                try {
                    const formData = new FormData();
                    formData.append('file', file);

                    const response = await fetch('/upload_file', {
                        method: 'POST',
                        body: formData
                    });

                    if (response.ok) {
                        const result = await response.json();
                        uploadedFiles.push(result.encoded_data);
                        displayUploadedFile(result.filename, result.size);
                        showToast(`File "${result.filename}" uploaded successfully!`, 'success');
                    } else {
                        throw new Error('Upload failed');
                    }
                } catch (error) {
                    console.error('File upload error:', error);
                    showToast(`Failed to upload "${file.name}"`, 'error');
                }
            }
        }

        function displayUploadedFile(filename, size) {
            const fileDiv = document.createElement('div');
            fileDiv.className = 'uploaded-file';
            fileDiv.innerHTML = `
                <span>📄 ${filename} (${Math.round(size / 1024)}KB)</span>
                <button class="file-remove" onclick="removeFile(this, '${filename}')">×</button>
            `;
            uploadedFilesContainer.appendChild(fileDiv);
        }

        function removeFile(button, filename) {
            // Remove from uploaded files array
            uploadedFiles = uploadedFiles.filter(file => !file.data || file.filename !== filename);
            // Remove from UI
            button.parentElement.remove();
            showToast(`Removed "${filename}"`, 'warning');
        }

        // Initialize audio visualization
        const numBars = 32;
        for (let i = 0; i < numBars; i++) {
            const box = document.createElement('div');
            box.className = 'box';
            boxContainer.appendChild(box);
        }

        // SVG Icons for mute toggle
        const micIconSVG = `
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                <line x1="12" y1="19" x2="12" y2="23"></line>
                <line x1="8" y1="23" x2="16" y2="23"></line>
            </svg>`;

        const micMutedIconSVG = `
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                <line x1="12" y1="19" x2="12" y2="23"></line>
                <line x1="8" y1="23" x2="16" y2="23"></line>
                <line x1="1" y1="1" x2="23" y2="23"></line>
            </svg>`;

        function updateButtonState() {
            if (peerConnection && (peerConnection.connectionState === 'connecting' || peerConnection.connectionState === 'new')) {
                startButton.innerHTML = `
                    <div class="spinner"></div>
                    <span>Connecting to AI Therapist...</span>
                `;
                startButton.disabled = true;
                startButton.classList.remove('danger');
            } else if (peerConnection && peerConnection.connectionState === 'connected') {
                const pulseContainer = document.createElement('div');
                pulseContainer.className = 'pulse-container';
                pulseContainer.innerHTML = `
                    <div class="pulse-circle"></div>
                    <span>🔊 End Voice Session</span>
                `;

                const muteToggle = document.createElement('div');
                muteToggle.className = 'mute-toggle';
                muteToggle.title = isMuted ? 'Unmute' : 'Mute';
                muteToggle.innerHTML = isMuted ? micMutedIconSVG : micIconSVG;
                muteToggle.addEventListener('click', toggleMute);

                startButton.innerHTML = '';
                startButton.appendChild(pulseContainer);
                startButton.appendChild(muteToggle);
                startButton.disabled = false;
                startButton.classList.add('danger');
            } else {
                startButton.innerHTML = '🎤 Start Voice Session';
                startButton.disabled = false;
                startButton.classList.remove('danger');
            }
        }

        function updateVideoButtonState() {
            if (videoPeerConnection && videoPeerConnection.connectionState === 'connected') {
                startVideoButton.innerHTML = '🔴 End Video Session';
                startVideoButton.classList.add('danger');
                startVideoButton.disabled = false;
                toggleCameraBtn.style.display = 'inline-flex';
                toggleMicBtn.style.display = 'inline-flex';
            } else if (videoPeerConnection && (videoPeerConnection.connectionState === 'connecting' || videoPeerConnection.connectionState === 'new')) {
                startVideoButton.innerHTML = `
                    <div class="spinner"></div>
                    <span>Connecting...</span>
                `;
                startVideoButton.disabled = true;
                startVideoButton.classList.remove('danger');
            } else {
                startVideoButton.innerHTML = '📹 Start Video Session';
                startVideoButton.classList.remove('danger');
                startVideoButton.disabled = false;
                toggleCameraBtn.style.display = 'none';
                toggleMicBtn.style.display = 'none';
            }
        }

        function showToast(message, type = 'error') {
            const toast = document.getElementById('error-toast');
            toast.textContent = message;
            toast.className = `toast ${type}`;
            toast.style.display = 'block';

            setTimeout(() => {
                toast.style.display = 'none';
            }, 5000);
        }

        function showStatus(message) {
            statusText.textContent = message;
            statusIndicator.classList.add('visible');
            
            setTimeout(() => {
                statusIndicator.classList.remove('visible');
            }, 3000);
        }

        function toggleMute(event) {
            event.stopPropagation();
            if (!peerConnection || peerConnection.connectionState !== 'connected') return;

            isMuted = !isMuted;
            console.log("Mute toggled:", isMuted);

            peerConnection.getSenders().forEach(sender => {
                if (sender.track && sender.track.kind === 'audio') {
                    sender.track.enabled = !isMuted;
                    console.log(`Audio track ${sender.track.id} enabled: ${!isMuted}`);
                }
            });

            updateButtonState();
        }

        // Audio-only WebRTC setup
        async function setupWebRTC() {
            const config = RTC_CONFIGURATION;
            peerConnection = new RTCPeerConnection(config);
            webrtc_id = Math.random().toString(36).substring(7);

            const timeoutId = setTimeout(() => {
                showToast("Connection is taking longer than usual. Please check your internet connection.", 'warning');
            }, 20000);

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: 16000
                    } 
                });
                
                stream.getTracks().forEach(track => peerConnection.addTrack(track, stream));

                if (!audioContext || audioContext.state === 'closed') {
                    audioContext = new AudioContext({ sampleRate: 24000 });
                }
                
                if (source_input) {
                    try { source_input.disconnect(); } catch (e) { console.warn("Error disconnecting previous input source:", e); }
                    source_input = null;
                }
                
                source_input = audioContext.createMediaStreamSource(stream);
                analyser_input = audioContext.createAnalyser();
                source_input.connect(analyser_input);
                analyser_input.fftSize = 64;
                dataArray_input = new Uint8Array(analyser_input.frequencyBinCount);
                updateAudioLevel();

                peerConnection.addEventListener('connectionstatechange', () => {
                    console.log('Voice connection state:', peerConnection.connectionState);
                    if (peerConnection.connectionState === 'connected') {
                        clearTimeout(timeoutId);
                        showToast('Connected to AI Therapist Voice!', 'success');
                        showStatus('Voice session active - say "bye" to end');
                        if (analyser_input) updateAudioLevel();
                        if (analyser) updateVisualization();
                    } else if (['disconnected', 'failed', 'closed'].includes(peerConnection.connectionState)) {
                        showStatus('Voice session ended');
                        endSession();
                    }
                    updateButtonState();
                });

                peerConnection.onicecandidate = ({ candidate }) => {
                    if (candidate) {
                        console.debug("Sending ICE candidate", candidate);
                        fetch('/audio/webrtc/offer', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                candidate: candidate.toJSON(),
                                webrtc_id: webrtc_id,
                                type: "ice-candidate",
                            })
                        });
                    }
                };

                peerConnection.addEventListener('track', (evt) => {
                    if (evt.track.kind === 'audio' && audioOutput) {
                        if (audioOutput.srcObject !== evt.streams[0]) {
                            audioOutput.srcObject = evt.streams[0];
                            audioOutput.play().catch(e => console.error("Audio play failed:", e));

                            if (!audioContext || audioContext.state === 'closed') {
                                console.warn("AudioContext not ready for output track analysis.");
                                return;
                            }
                            
                            if (source_output) {
                                try { source_output.disconnect(); } catch (e) { console.warn("Error disconnecting previous output source:", e); }
                                source_output = null;
                            }
                            
                            source_output = audioContext.createMediaStreamSource(evt.streams[0]);
                            analyser = audioContext.createAnalyser();
                            source_output.connect(analyser);
                            analyser.fftSize = 2048;
                            dataArray = new Uint8Array(analyser.frequencyBinCount);
                            updateVisualization();
                        }
                    }
                });

                dataChannel = peerConnection.createDataChannel('control');
                dataChannel.onmessage = (event) => {
                    const eventJson = JSON.parse(event.data);
                    if (eventJson.type === "error") {
                        showToast(eventJson.message, 'error');
                    } else if (eventJson.type === "send_input") {
                        fetch('/input_hook', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                webrtc_id: webrtc_id,
                                voice_name: voiceSelect.value,
                                mode: "audio"
                            })
                        });
                    }
                };

                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);

                const response = await fetch('/audio/webrtc/offer', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sdp: peerConnection.localDescription.sdp,
                        type: peerConnection.localDescription.type,
                        webrtc_id: webrtc_id,
                    })
                });

                const serverResponse = await response.json();

                if (serverResponse.status === 'failed') {
                    const errorMsg = serverResponse.meta.error === 'concurrency_limit_reached'
                        ? `Too many active sessions. Maximum limit is ${serverResponse.meta.limit}. Please try again later.`
                        : `Connection failed: ${serverResponse.meta.error}`;
                    showToast(errorMsg, 'error');
                    stopWebRTC();
                    return;
                }

                await peerConnection.setRemoteDescription(serverResponse);

            } catch (err) {
                clearTimeout(timeoutId);
                console.error('Error setting up Voice WebRTC:', err);
                showToast('Failed to establish voice connection. Please check your microphone permissions and try again.', 'error');
                stopWebRTC();
            }
        }

        // Video WebRTC setup
        async function setupVideoWebRTC() {
            const config = RTC_CONFIGURATION;
            videoPeerConnection = new RTCPeerConnection(config);
            video_webrtc_id = Math.random().toString(36).substring(7);

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: 'user'
                    },
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: 16000
                    } 
                });
                
                localStream = stream;
                localVideo.srcObject = stream;
                
                stream.getTracks().forEach(track => videoPeerConnection.addTrack(track, stream));

                videoPeerConnection.addEventListener('connectionstatechange', () => {
                    console.log('Video connection state:', videoPeerConnection.connectionState);
                    if (videoPeerConnection.connectionState === 'connected') {
                        showToast('Connected to AI Therapist Video!', 'success');
                        showStatus('Video session active - say "bye" to end');
                        localStatus.textContent = 'Connected';
                    } else if (['disconnected', 'failed', 'closed'].includes(videoPeerConnection.connectionState)) {
                        showStatus('Video session ended');
                        localStatus.textContent = 'Camera Ready';
                        endVideoSession();
                    }
                    updateVideoButtonState();
                });

                videoPeerConnection.onicecandidate = ({ candidate }) => {
                    if (candidate) {
                        fetch('/video/webrtc/offer', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                candidate: candidate.toJSON(),
                                webrtc_id: video_webrtc_id,
                                type: "ice-candidate",
                            })
                        });
                    }
                };

                // Audio from AI (no video from AI)
                videoPeerConnection.addEventListener('track', (evt) => {
                    if (evt.track.kind === 'audio') {
                        const audioElement = new Audio();
                        audioElement.srcObject = evt.streams[0];
                        audioElement.play().catch(e => console.error("Video audio play failed:", e));
                    }
                });

                videoDataChannel = videoPeerConnection.createDataChannel('control');
                videoDataChannel.onmessage = (event) => {
                    const eventJson = JSON.parse(event.data);
                    if (eventJson.type === "error") {
                        showToast(eventJson.message, 'error');
                    } else if (eventJson.type === "send_input") {
                        fetch('/input_hook', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                webrtc_id: video_webrtc_id,
                                voice_name: videoVoiceSelect.value,
                                mode: "video",
                                uploaded_files: uploadedFiles
                            })
                        });
                    }
                };

                const offer = await videoPeerConnection.createOffer();
                await videoPeerConnection.setLocalDescription(offer);

                const response = await fetch('/video/webrtc/offer', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sdp: videoPeerConnection.localDescription.sdp,
                        type: videoPeerConnection.localDescription.type,
                        webrtc_id: video_webrtc_id,
                    })
                });

                const serverResponse = await response.json();

                if (serverResponse.status === 'failed') {
                    const errorMsg = serverResponse.meta.error === 'concurrency_limit_reached'
                        ? `Too many active video sessions. Please try again later.`
                        : `Video connection failed: ${serverResponse.meta.error}`;
                    showToast(errorMsg, 'error');
                    stopVideoWebRTC();
                    return;
                }

                await videoPeerConnection.setRemoteDescription(serverResponse);

            } catch (err) {
                console.error('Error setting up Video WebRTC:', err);
                showToast('Failed to establish video connection. Please check your camera and microphone permissions.', 'error');
                stopVideoWebRTC();
            }
        }

        function updateVisualization() {
            if (!analyser || !peerConnection || !['connected', 'connecting'].includes(peerConnection.connectionState)) {
                const bars = document.querySelectorAll('.box');
                bars.forEach(bar => bar.style.transform = 'scaleY(0.1)');
                return;
            }

            analyser.getByteFrequencyData(dataArray);
            const bars = document.querySelectorAll('.box');

            for (let i = 0; i < bars.length; i++) {
                const barHeight = (dataArray[i] / 255) * 2;
                bars[i].style.transform = `scaleY(${Math.max(0.1, barHeight)})`;
            }

            requestAnimationFrame(updateVisualization);
        }

        function updateAudioLevel() {
            if (!analyser_input || !peerConnection || !['connected', 'connecting'].includes(peerConnection.connectionState)) {
                const pulseCircle = document.querySelector('.pulse-circle');
                if (pulseCircle) {
                    pulseCircle.style.setProperty('--audio-level', 1);
                }
                return;
            }
            
            analyser_input.getByteFrequencyData(dataArray_input);
            const average = Array.from(dataArray_input).reduce((a, b) => a + b, 0) / dataArray_input.length;
            const audioLevel = average / 255;

            const pulseCircle = document.querySelector('.pulse-circle');
            if (pulseCircle) {
                pulseCircle.style.setProperty('--audio-level', 1 + audioLevel);
            }

            requestAnimationFrame(updateAudioLevel);
        }

        function endSession() {
            fetch('/end_session', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            }).catch(e => console.warn('Could not notify server of session end:', e));
        }

        function endVideoSession() {
            fetch('/end_session', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            }).catch(e => console.warn('Could not notify server of video session end:', e));
        }

        function stopWebRTC() {
            console.log("Stopping Audio WebRTC session");
            
            if (peerConnection) {
                peerConnection.getSenders().forEach(sender => {
                    if (sender.track) {
                        sender.track.stop();
                    }
                });
                
                peerConnection.ontrack = null;
                peerConnection.onicegatheringstatechange = null;
                peerConnection.onconnectionstatechange = null;

                if (dataChannel) {
                    dataChannel.onmessage = null;
                    try { dataChannel.close(); } catch (e) { console.warn("Error closing data channel:", e); }
                    dataChannel = null;
                }
                
                try { peerConnection.close(); } catch (e) { console.warn("Error closing peer connection:", e); }
                peerConnection = null;
            }

            if (audioOutput) {
                audioOutput.pause();
                audioOutput.srcObject = null;
            }

            if (source_input) {
                try { source_input.disconnect(); } catch (e) { console.warn("Error disconnecting input source:", e); }
                source_input = null;
            }
            
            if (source_output) {
                try { source_output.disconnect(); } catch (e) { console.warn("Error disconnecting output source:", e); }
                source_output = null;
            }

            if (audioContext && audioContext.state !== 'closed') {
                audioContext.close().then(() => {
                    console.log("AudioContext closed successfully.");
                    audioContext = null;
                }).catch(e => {
                    console.error("Error closing AudioContext:", e);
                    audioContext = null;
                });
            } else {
                audioContext = null;
            }

            analyser_input = null;
            dataArray_input = null;
            analyser = null;
            dataArray = null;
            isMuted = false;
            isRecording = false;
            
            updateButtonState();

            const bars = document.querySelectorAll('.box');
            bars.forEach(bar => bar.style.transform = 'scaleY(0.1)');
            
            const pulseCircle = document.querySelector('.pulse-circle');
            if (pulseCircle) {
                pulseCircle.style.setProperty('--audio-level', 1);
            }
            
            showStatus('Voice session ended');
            endSession();
        }

        function stopVideoWebRTC() {
            console.log("Stopping Video WebRTC session");
            
            if (videoPeerConnection) {
                videoPeerConnection.getSenders().forEach(sender => {
                    if (sender.track) {
                        sender.track.stop();
                    }
                });
                
                videoPeerConnection.ontrack = null;
                videoPeerConnection.onconnectionstatechange = null;

                if (videoDataChannel) {
                    videoDataChannel.onmessage = null;
                    try { videoDataChannel.close(); } catch (e) { console.warn("Error closing video data channel:", e); }
                    videoDataChannel = null;
                }
                
                try { videoPeerConnection.close(); } catch (e) { console.warn("Error closing video peer connection:", e); }
                videoPeerConnection = null;
            }

            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
            }

            localVideo.srcObject = null;
            
            isVideoRecording = false;
            isCameraOn = true;
            
            updateVideoButtonState();
            showStatus('Video session ended');
            endVideoSession();
        }

        // Event listeners for buttons
        startButton.addEventListener('click', (event) => {
            // Check if clicking on mute toggle
            if (event.target.closest('.mute-toggle')) {
                return; // Let the mute toggle handle it
            }

            if (peerConnection && peerConnection.connectionState === 'connected') {
                console.log("Ending voice session");
                stopWebRTC();
            } else if (!peerConnection || ['new', 'closed', 'failed', 'disconnected'].includes(peerConnection.connectionState)) {
                console.log("Starting voice session");
                setupWebRTC();
                isRecording = true;
                updateButtonState();
            }
        });

        startVideoButton.addEventListener('click', () => {
            if (videoPeerConnection && videoPeerConnection.connectionState === 'connected') {
                console.log("Ending video session");
                stopVideoWebRTC();
            } else if (!videoPeerConnection || ['new', 'closed', 'failed', 'disconnected'].includes(videoPeerConnection.connectionState)) {
                console.log("Starting video session");
                setupVideoWebRTC();
                isVideoRecording = true;
                updateVideoButtonState();
            }
        });

        toggleCameraBtn.addEventListener('click', () => {
            if (localStream) {
                const videoTrack = localStream.getVideoTracks()[0];
                if (videoTrack) {
                    isCameraOn = !isCameraOn;
                    videoTrack.enabled = isCameraOn;
                    toggleCameraBtn.innerHTML = isCameraOn ? '📷 Turn Off Camera' : '📷 Turn On Camera';
                    localStatus.textContent = isCameraOn ? 'Camera On' : 'Camera Off';
                }
            }
        });

        toggleMicBtn.addEventListener('click', () => {
            if (localStream) {
                const audioTrack = localStream.getAudioTracks()[0];
                if (audioTrack) {
                    const isMicOn = audioTrack.enabled;
                    audioTrack.enabled = !isMicOn;
                    toggleMicBtn.innerHTML = !isMicOn ? '🎤 Mute Mic' : '🎤 Unmute Mic';
                }
            }
        });

        // Text Mode Implementation (same as before)
        let textWebSocket = null;
        let recognition = null;
        let isListening = false;

        function initializeTextMode() {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            
            if (SpeechRecognition) {
                recognition = new SpeechRecognition();
                recognition.continuous = false;
                recognition.lang = 'en-US';
                recognition.interimResults = false;
                recognition.maxAlternatives = 1;

                recognition.onstart = () => {
                    isListening = true;
                    micButton.classList.add('active');
                    showStatus('Listening...');
                };

                recognition.onresult = (event) => {
                    const transcript = event.results[0][0].transcript;
                    userInput.value = transcript;
                    showStatus(`Recognized: "${transcript}"`);
                };

                recognition.onend = () => {
                    isListening = false;
                    micButton.classList.remove('active');
                };

                recognition.onerror = (event) => {
                    isListening = false;
                    micButton.classList.remove('active');
                    showToast(`Speech recognition error: ${event.error}`, 'warning');
                };
            } else {
                micButton.style.display = 'none';
                showStatus('Speech recognition not supported in this browser');
            }

            userInput.addEventListener('input', () => {
                userInput.style.height = 'auto';
                userInput.style.height = (userInput.scrollHeight) + 'px';
            });

            userInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendTextMessage();
                }
            });

            sendButton.addEventListener('click', sendTextMessage);
            micButton.addEventListener('click', toggleTextListening);
        }

        function initializeVideoMode() {
            // Video mode initialization
            showStatus('Video mode ready - upload files and click start to begin session');
        }

        function toggleTextListening() {
            if (!recognition) return;
            
            if (isListening) {
                recognition.stop();
            } else {
                recognition.start();
            }
        }

        function sendTextMessage() {
            const message = userInput.value.trim();
            if (!message) return;

            addMessageToChat(message, 'user');
            userInput.value = '';
            userInput.style.height = 'auto';
            
            connectWebSocketAndSend(message);
        }

        function connectWebSocketAndSend(message) {
            if (textWebSocket) {
                textWebSocket.close();
            }

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            textWebSocket = new WebSocket(wsUrl);
            
            textWebSocket.onopen = () => {
                showStatus('Connected to AI Therapist');
                textWebSocket.send(message);
            };

            textWebSocket.onmessage = (event) => {
                const aiMessageDiv = document.querySelector('.ai-message:last-child .bubble p');
                if (aiMessageDiv && aiMessageDiv.textContent === 'Thinking...') {
                    aiMessageDiv.textContent = event.data;
                } else {
                    if (!document.querySelector('.ai-message:last-child .bubble p')?.textContent?.endsWith('...')) {
                        addMessageToChat('Thinking...', 'ai');
                    }
                    const lastAiMessage = document.querySelector('.ai-message:last-child .bubble p');
                    if (lastAiMessage) {
                        if (lastAiMessage.textContent === 'Thinking...') {
                            lastAiMessage.textContent = event.data;
                        } else {
                            lastAiMessage.textContent += event.data;
                        }
                    }
                }
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            };

            textWebSocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                showToast('Connection error. Please try again.', 'error');
            };

            textWebSocket.onclose = () => {
                showStatus('Disconnected');
            };

            addMessageToChat('Thinking...', 'ai');
        }

        function addMessageToChat(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            const avatarDiv = document.createElement('div');
            avatarDiv.className = 'avatar';
            avatarDiv.textContent = sender === 'user' ? '👤' : '🤖';
            
            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'bubble';
            
            const paragraph = document.createElement('p');
            paragraph.textContent = text;
            bubbleDiv.appendChild(paragraph);
            
            messageDiv.appendChild(avatarDiv);
            messageDiv.appendChild(bubbleDiv);
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Initialize
        updateButtonState();
        updateVideoButtonState();
        initializeTextMode();

        console.log("AI Therapist with Voice, Video & Text initialized successfully!");
    </script>
</body>

</html>
# 🎤 Mai Voice Agent

<div align="center">

![<PERSON>](frontend/Assets/mai.gif)

**A sophisticated real-time voice and video AI assistant powered by Google's Gemini 2.0 Flash**

*Experience natural conversations with <PERSON> through voice, video, and text interactions*

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-Latest-green.svg)](https://fastapi.tiangolo.com)
[![WebRTC](https://img.shields.io/badge/WebRTC-Enabled-orange.svg)](https://webrtc.org)
[![Gemini](https://img.shields.io/badge/Gemini-2.0%20Flash-purple.svg)](https://ai.google.dev)

</div>

---

**<PERSON>** is an intelligent, voice-to-voice AI customer service assistant for **Critical Future** — a world-leading AI & Strategy Development Agency. Mai is designed to be indistinguishable from a top-tier human assistant: emotionally aware, naturally spoken, and incredibly helpful.

## ✨ Features

### 🎙️ **Real-time Voice Chat**
- Natural conversation flow with ultra-low latency
- Advanced voice synthesis with multiple voice options (Aoede, Charon, Fenrir, Kore, Puck)
- Real-time audio processing and response generation
- Visual wave animations during conversations

### 📹 **Interactive Video Chat**
- Mai can see you through your camera
- Responds to visual cues and non-verbal communication
- Real-time video processing with Gemini Vision
- Seamless audio-video synchronization

### 💬 **Intelligent Text Chat**
- Traditional text-based conversations
- Rich message formatting and history
- Instant responses with typing indicators
- Conversation context preservation

### 🎨 **Modern User Experience**
- Sleek dark theme interface
- Responsive design for all devices
- Smooth animations and transitions
- Intuitive controls and navigation

### 📧 **Email Integration**
- Send conversation summaries
- Share important insights
- Automated email formatting
- Secure email delivery

---

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Modern web browser with WebRTC support
- Microphone and camera (for voice/video features)
- Google Gemini API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd "Mai Voice Agent"
   ```

2. **Set up Python environment**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Configure environment variables**
   Create a `.env` file in the backend directory:
   ```env
   GEMINI_API_KEY=your_gemini_api_key_here
   EMAIL_USER=<EMAIL>
   EMAIL_PASSWORD=your_app_password
   ```

4. **Launch the application**
   ```bash
   python main.py
   ```

5. **Open your browser**
   Navigate to `http://localhost:8000` and start chatting with Mai!

---

## 🏗️ Architecture

### Technology Stack

| Component | Technology | Purpose |
|-----------|------------|---------|
| **Backend** | FastAPI + Python | High-performance async API server |
| **Real-time Communication** | WebRTC + FastRTC | Ultra-low latency audio/video streaming |
| **AI Engine** | Google Gemini 2.0 Flash | Advanced language and vision processing |
| **Frontend** | HTML5 + CSS3 + JavaScript | Modern responsive web interface |
| **Voice Synthesis** | Google's Neural Voices | Natural-sounding speech generation |

### Project Structure

```
Mai Voice Agent/
├── 🔧 backend/
│   ├── main.py              # 🚀 FastAPI application entry point
│   ├── routes.py            # 🛣️  API routes and endpoints
│   ├── webrtc_handler.py    # 📡 WebRTC and voice/video handling
│   ├── ai_handlers.py       # 🤖 Gemini AI integration
│   ├── config.py            # ⚙️  Configuration management
│   ├── models.py            # 📋 Pydantic data models
│   ├── email_service.py     # 📧 Email functionality
│   └── requirements.txt     # 📦 Python dependencies
├── 🎨 frontend/
│   ├── index.html           # 🏠 Main application interface
│   ├── script.js            # ⚡ Frontend JavaScript logic
│   ├── styles.css           # 🎭 Styling and animations
│   └── Assets/
│       ├── mai.gif          # 👋 Mai's animated avatar
│       └── cf_logo.png      # 🏢 Critical Future logo
└── 📖 README.md             # 📚 This documentation
```

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Google Gemini API key
- Email credentials (Gmail app password recommended)

### Local Development

1. **Clone and Setup**
   ```bash
   cd "Mai Voice Agent"
   cp .env.example .env
   ```

2. **Configure Environment**
   Edit `.env` with your credentials:
   ```env
   GEMINI_API_KEY=your_gemini_api_key_here
   EMAIL_ADDRESS=<EMAIL>
   EMAIL_PASSWORD=lceg dmyy fvwm fkor
   ```

3. **Install Dependencies**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

4. **Run the Application**
   ```bash
   python main.py
   ```

5. **Run Local Tests**
   ```bash
   cd ..
   python test_local.py
   ```

6. **Access Mai**
   Open http://localhost:8000 in your browser

## 🧪 Testing

### Local Testing
```bash
# Run comprehensive test suite
python test_local.py
```

Tests cover:
- ✅ Configuration validation
- ✅ Data models
- ✅ AI handlers
- ✅ Email service
- ✅ API routes
- ✅ Main application

### Manual Testing
1. **Text Chat**: Visit `/` and use the chat interface
2. **Voice Chat**: Click "Voice Chat" mode (requires microphone permission)
3. **Video Chat**: Click "Video Chat" mode (requires camera/microphone permission)
4. **Contact Form**: Fill out and submit the contact form
5. **Health Check**: Visit `/api/health` for system status

## 🌐 Railway Deployment

### Step 1: Prepare Repository

1. **Create GitHub Repository**
   ```bash
   git init
   git add .
   git commit -m "Initial Mai Voice Agent setup"
   git remote add origin https://github.com/yourusername/mai-voice-agent.git
   git push -u origin main
   ```

### Step 2: Deploy to Railway

1. **Connect to Railway**
   - Go to [Railway.app](https://railway.app)
   - Click "New Project" → "Deploy from GitHub repo"
   - Select your Mai Voice Agent repository

2. **Configure Environment Variables**
   In Railway dashboard, add these variables:
   ```
   GEMINI_API_KEY=your_gemini_api_key_here
   EMAIL_ADDRESS=<EMAIL>
   EMAIL_PASSWORD=lceg dmyy fvwm fkor
   SMTP_SERVER=smtp.gmail.com
   SMTP_PORT=587
   PORT=8000
   ```

3. **Deploy**
   - Railway will automatically detect the Dockerfile
   - Build and deployment will start automatically
   - Your app will be available at `https://your-app-name.railway.app`

### Step 3: Custom Domain (Optional)

1. In Railway dashboard, go to Settings → Domains
2. Add your custom domain
3. Update DNS records as instructed

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `GEMINI_API_KEY` | Google Gemini API key | ✅ | - |
| `EMAIL_USER` | Email address for sending messages | ⚠️ | - |
| `EMAIL_PASSWORD` | App password for email service | ⚠️ | - |

### Voice Options
- **Aoede** - Warm and friendly (default)
- **Charon** - Deep and authoritative
- **Fenrir** - Energetic and dynamic
- **Kore** - Calm and soothing
- **Puck** - Playful and cheerful

---

## 🎯 Usage Guide

### 🎤 Voice Chat
1. Click the **🎙️ Voice Chat** tab
2. Press **Start Voice Chat** button
3. Allow microphone permissions
4. Start speaking naturally to Mai
5. Watch the wave animations as Mai responds

### 📹 Video Chat
1. Click the **📹 Video Chat** tab
2. Press **Start Video Chat** button
3. Allow camera and microphone permissions
4. Mai can now see and hear you
5. Engage in natural conversation with visual context

### 💬 Text Chat
1. Click the **💬 Text Chat** tab
2. Type your message in the input field
3. Press Enter or click Send
4. Enjoy instant text-based conversations

### 📧 Email Features
1. Use the email integration to share conversations
2. Send summaries and insights
3. Automated formatting for professional communication

---

## 🔧 Development

### Key Features
- **Modular Architecture**: Easy to extend and maintain
- **Real-time Processing**: Ultra-low latency communication
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Error Handling**: Robust error recovery and user feedback
- **Security**: Secure API key management and data handling

### API Endpoints
- `GET /` - Main application interface
- `POST /api/chat` - Text chat endpoint
- `POST /api/input_hook` - WebRTC session initialization
- `POST /audio/webrtc/offer` - Audio stream WebRTC negotiation
- `POST /video/webrtc/offer` - Video stream WebRTC negotiation
- `POST /api/email/send` - Email sending functionality

---

## 🤝 Contributing

We welcome contributions! Please feel free to submit issues, feature requests, or pull requests.

---

## 📄 License

Created with ❤️ by **Critical Future**

![Critical Future Logo](frontend/Assets/cf_logo.png)

---

<div align="center">

**Experience the future of AI conversation with Mai Voice Agent**

*Real-time • Intelligent • Interactive*

</div>
| `EMAIL_ADDRESS` | Sender email address | ✅ | <EMAIL> |
| `EMAIL_PASSWORD` | Email app password | ✅ | - |
| `SMTP_SERVER` | SMTP server | ❌ | smtp.gmail.com |
| `SMTP_PORT` | SMTP port | ❌ | 587 |
| `PORT` | Server port | ❌ | 8000 |
| `USE_VERTEX_AI` | Use Vertex AI instead of API | ❌ | false |
| `GOOGLE_CLOUD_PROJECT` | GCP project ID (if using Vertex AI) | ❌ | - |

### Email Setup

For Gmail (recommended):
1. Enable 2-factor authentication
2. Generate an app password: Google Account → Security → App passwords
3. Use the app password as `EMAIL_PASSWORD`

## 🎯 Mai's Capabilities

### Core Responsibilities

1. **Greet callers** with warmth and professionalism
2. **Engage naturally** to understand their reason for calling
3. **Capture and confirm** key details:
   - Full name
   - Company name (if relevant)
   - Email address
   - Phone number (optional)
   - Purpose of enquiry or interest
4. **Ask relevant follow-up questions** to clarify their needs
5. **Repeat back and confirm** the captured details
6. **Generate a structured summary** of the conversation
7. **Trigger follow-up emails** to both caller and Critical Future team

### Email Follow-ups

**To the Caller:**
- Confirmation that Critical Future received their message
- Summary of their enquiry
- Note that a team member will be in touch soon

**To Critical Future Team:**
- Caller's contact details
- Conversation summary in bullet points
- Optional full call transcript

## 🛠️ API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/` | GET | Main interface |
| `/chat` | POST | Text chat with Mai |
| `/ws` | WebSocket | Real-time text chat |
| `/submit_contact` | POST | Submit contact form |
| `/input_hook` | POST | Initialize voice chat |
| `/end_session` | POST | End active session |
| `/health` | GET | Health check |
| `/debug` | GET | Debug information |
| `/api/voices` | GET | Available voices |

## 🔍 Troubleshooting

### Common Issues

1. **Voice chat not working**
   - Check if FastRTC dependencies are installed
   - Verify microphone permissions in browser
   - Use text chat as fallback

2. **Email not sending**
   - Verify email credentials in environment variables
   - Check Gmail app password setup
   - Review server logs for SMTP errors

3. **Deployment issues**
   - Ensure all environment variables are set in Railway
   - Check build logs for dependency errors
   - Verify Dockerfile builds locally

### Debug Mode

Access `/debug` endpoint to check:
- Configuration status
- Available features
- Active sessions
- System information

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Company**: Critical Future
- **Website**: https://criticalfuture.co.uk

## 📄 License

This project is proprietary software of Critical Future. All rights reserved.

---

**Mai Voice Agent** - Powered by Critical Future's AI expertise and Google Gemini technology.

## 🚀 Complete Deployment Guide

### Prerequisites Checklist

- [ ] Google Gemini API key obtained
- [ ] Gmail app password generated
- [ ] GitHub account ready
- [ ] Railway account created

### Step-by-Step Railway Deployment

1. **Prepare Your Repository**
   ```bash
   # Navigate to the project directory
   cd "Mai Voice Agent"

   # Initialize git repository
   git init

   # Add all files
   git add .

   # Commit changes
   git commit -m "Initial Mai Voice Agent deployment"

   # Add your GitHub repository as remote
   git remote add origin https://github.com/yourusername/mai-voice-agent.git

   # Push to GitHub
   git push -u origin main
   ```

2. **Deploy on Railway**
   - Visit [railway.app](https://railway.app)
   - Click "New Project"
   - Select "Deploy from GitHub repo"
   - Choose your mai-voice-agent repository
   - Railway will automatically detect the Dockerfile

3. **Configure Environment Variables in Railway**
   Go to your project → Variables tab and add:
   ```
   GEMINI_API_KEY=your_actual_gemini_api_key
   EMAIL_ADDRESS=<EMAIL>
   EMAIL_PASSWORD=lceg dmyy fvwm fkor
   SMTP_SERVER=smtp.gmail.com
   SMTP_PORT=587
   PORT=8000
   ```

4. **Monitor Deployment**
   - Check the "Deployments" tab for build progress
   - View logs for any errors
   - Once deployed, your app will be available at the provided URL

5. **Test Your Deployment**
   - Visit your Railway app URL
   - Test text chat functionality
   - Try the contact form
   - Verify email sending works

### Environment Variables for Railway

Copy these exact variables to your Railway project:

```env
GEMINI_API_KEY=your_gemini_api_key_here
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=lceg dmyy fvwm fkor
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
PORT=8000
USE_VERTEX_AI=false
```

### Post-Deployment Verification

1. **Health Check**: Visit `https://your-app.railway.app/health`
2. **Debug Info**: Visit `https://your-app.railway.app/debug`
3. **Test Chat**: Use the main interface to chat with Mai
4. **Test Email**: Submit the contact form to verify email functionality

Your Mai Voice Agent is now live and ready to assist Critical Future's customers! 🎉

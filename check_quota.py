#!/usr/bin/env python3
"""
Check Gemini API quota status
"""

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_gemini_quota():
    """Check Gemini API quota and usage"""
    api_key = os.getenv('GEMINI_API_KEY')
    
    if not api_key:
        print("❌ No GEMINI_API_KEY found in environment")
        return
    
    print(f"🔑 Using API Key: {api_key[:20]}...")
    
    # Test API with a simple request using the correct model
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"
    
    headers = {
        'Content-Type': 'application/json',
    }
    
    data = {
        "contents": [{
            "parts": [{
                "text": "Hello, this is a test to check quota status."
            }]
        }]
    }
    
    try:
        print("🔍 Testing API connection...")
        response = requests.post(url, json=data, headers=headers)
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📋 Response Headers:")
        for key, value in response.headers.items():
            if 'quota' in key.lower() or 'limit' in key.lower() or 'usage' in key.lower():
                print(f"   {key}: {value}")
        
        if response.status_code == 200:
            print("✅ API is working - quota available")
            result = response.json()
            if 'candidates' in result:
                print(f"📝 Response: {result['candidates'][0]['content']['parts'][0]['text'][:100]}...")
        elif response.status_code == 429:
            print("❌ Quota exceeded - rate limited")
            print(f"📄 Error: {response.text}")
        elif response.status_code == 403:
            print("❌ Quota exceeded or API key invalid")
            print(f"📄 Error: {response.text}")
        else:
            print(f"⚠️ Unexpected status: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error checking quota: {e}")

if __name__ == "__main__":
    check_gemini_quota()
